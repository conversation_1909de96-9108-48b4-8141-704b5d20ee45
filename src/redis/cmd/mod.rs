use anyhow::Result;
use api_version::KvFormat;
use kvproto::kvrpcpb::{Context, RedisRequest, RedisResponse};
use tikv_kv::Engine;

use crate::{
    redis::{
        error::RedisError, cmd::string::{CommandSet, CommandGet},
        
    },
    storage::{lock_manager::LockManager, Storage},
};

pub mod string;

pub enum Command {
    Get(CommandGet),
    Set(CommandSet),
}

impl Command {
    pub fn from_request(req: &RedisRequest) -> Result<Command, RedisError> {
        let command: Command = match req.get_cmd() {
            "GET" => Command::Get(CommandGet::parse(req)?),
            "SET" => Command::Set(CommandSet::parse(req)?),
            _ => return Err(RedisError::UnknownCmd),
        };
        Ok(command)
    }

    pub async fn apply<E, L, F>(
        self,
        ctx: Context,
        storage: Storage<E, L, F>,
    ) -> Result<RedisResponse>
    where
        E: Engine,
        L: LockManager,
        F: KvFormat,
    {
        match self {
            Command::Get(cmd) => cmd.execute(ctx, storage).await,
            Command::Set(mut cmd) => cmd.execute(ctx, storage).await,
        }
    }
}
