use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum RedisError {

    #[error("NOT_SUPPORT Not support command.")]
    UnknownCmd,
    
    #[error("ERR Syntax error")]
    SyntaxErr,

    #[error("ERR Invalid number of arguments specified for command.")]
    InvalidArg,

    #[error("WRONGTYPE Operation against a key holding the wrong kind of value")]
    WrongType,

    #[error("ERR failed to parse integer")]
    ParseErr,

    #[error("Err metadata is too short")]
    MetaDataErr,

    #[error("ERR Internal error")]
    InternalErr,

}