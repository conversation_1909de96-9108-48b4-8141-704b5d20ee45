# Require review from domain experts when the PR modified significant config files.
/components/batch-system/src/config.rs @tikv/tikv-configuration-reviewer
/components/cdc/src/config.rs @tikv/tikv-configuration-reviewer
/components/encryption/src/config.rs @tikv/tikv-configuration-reviewer
/components/engine_rocks/src/config.rs @tikv/tikv-configuration-reviewer
/components/engine_traits/src/config.rs @tikv/tikv-configuration-reviewer
/components/pd_client/src/config.rs @tikv/tikv-configuration-reviewer
/components/raftstore/src/coprocessor/config.rs @tikv/tikv-configuration-reviewer
/components/raftstore/src/store/config.rs @tikv/tikv-configuration-reviewer
/components/raftstore/src/store/worker/split_config.rs @tikv/tikv-configuration-reviewer
/components/resource_metering/src/config.rs @tikv/tikv-configuration-reviewer
/components/security/src/lib.rs @tikv/tikv-configuration-reviewer
/components/sst_importer/src/config.rs @tikv/tikv-configuration-reviewer
/components/tikv_util/src/config.rs @tikv/tikv-configuration-reviewer
/etc/config-template.toml @tikv/tikv-configuration-reviewer
/src/config.rs @tikv/tikv-configuration-reviewer
/src/coprocessor_v2/config.rs @tikv/tikv-configuration-reviewer
/src/server/config.rs @tikv/tikv-configuration-reviewer
/src/server/gc_worker/config.rs @tikv/tikv-configuration-reviewer
/src/server/lock_manager/config.rs @tikv/tikv-configuration-reviewer
/src/server/status_server/mod.rs @tikv/tikv-configuration-reviewer
/src/storage/config.rs @tikv/tikv-configuration-reviewer
