[package]
name = "backup"
version = "0.0.1"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
cloud-storage-grpc = ["external_storage_export/cloud-storage-grpc"]
cloud-storage-dylib = ["external_storage_export/cloud-storage-dylib"]
test-engine-kv-rocksdb = [
  "tikv/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "tikv/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "tikv/test-engines-rocksdb",
]
test-engines-panic = [
  "tikv/test-engines-panic",
]
tcmalloc = ["tikv/tcmalloc"]
jemalloc = ["tikv/jemalloc"]
mimalloc = ["tikv/mimalloc"]
snmalloc = ["tikv/snmalloc"]
portable = ["tikv/portable"]
sse = ["tikv/sse"]
mem-profiling = ["tikv/mem-profiling"]
failpoints = ["tikv/failpoints"]

[dependencies]
api_version = { workspace = true }
async-channel = "1.4"
causal_ts = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crc64fast = "0.1"
encryption = { workspace = true }
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
external_storage = { workspace = true }
external_storage_export = { workspace = true }
file_system = { workspace = true }
futures = "0.3"
futures-util = { version = "0.3", default-features = false, features = ["io"] }
grpcio = { workspace = true }
hex = "0.4"
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
online_config = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raftstore = { workspace = true }
security = { workspace = true }
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
# better to not use slog-global, but pass in the logger
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tidb_query_common = { workspace = true }
tikv = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread"] }
tokio-stream = "0.1"
txn_types = { workspace = true }
yatp = { workspace = true }

[dev-dependencies]
rand = "0.8"
tempfile = "3.0"
tokio = { version = "1.5", features = ["time", "macros"] }
