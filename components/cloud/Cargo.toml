[package]
name = "cloud"
version = "0.0.1"
edition = "2018"
publish = false

[dependencies]
async-trait = "0.1"
derive_more = "0.99.3"
error_code = { workspace = true }
futures-io = "0.3"
kvproto = { workspace = true }
lazy_static = "1.3"
openssl = "0.10"
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
protobuf = { version = "2.8", features = ["bytes"] }
rusoto_core = "0.46.0"
thiserror = "1.0"
tikv_util = { workspace = true }
url = "2.0"

[dev-dependencies]
fail = "0.5"
