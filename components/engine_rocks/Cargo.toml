[package]
name = "engine_rocks"
version = "0.0.1"
edition = "2018"
publish = false

[features]
jemalloc = ["rocksdb/jemalloc"]
portable = ["rocksdb/portable"]
sse = ["rocksdb/sse"]
failpoints = ["fail/failpoints"]

# Disables runtime checks of invariants required by RocksDB that are redundant
# with assertions inside RocksDB itself. This makes it possible to test those
# invariants in Rust tests, resulting an a panic instead of an abort, at the
# expense of an extra branch. This feature exists to mark those extra branches.
# The checks _can_ be disabled by enabling this feature, though it may not
# result in any real performance improvement to do so, and it will break
# the test suite.
#
# Probably these checks should be in the rust-rocksdb crate itself, in order
# to ensure the bindings are safe, but until that is done, they are here.
nortcheck = []

[dependencies]
api_version = { workspace = true }
case_macros = { workspace = true }
collections = { workspace = true }
derive_more = "0.99.3"
encryption = { workspace = true }
engine_traits = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.4.0"
log_wrappers = { workspace = true }
num_cpus = "1"
online_config = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = "2"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
regex = "1"
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog_derive = "0.2"
tempfile = "3.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
time = "0.1"
tracker = { workspace = true }
txn_types = { workspace = true }

[dependencies.rocksdb]
git = "https://github.com/tikv/rust-rocksdb.git"
package = "rocksdb"
features = ["encryption"]

[dev-dependencies]
rand = "0.8"
toml = "0.5"
