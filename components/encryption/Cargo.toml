[package]
name = "encryption"
version = "0.0.1"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]

[dependencies]
async-trait = "0.1"
byteorder = "1.2"
bytes = "1.0"
crc32fast = "1.2"
crossbeam = "0.8"
derive_more = "0.99.3"
engine_traits = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
futures = "0.3"
futures-util = { version = "0.3", default-features = false, features = ["std", "io"] }
hex = "0.4.2"
kvproto = { workspace = true }
lazy_static = "1.3"
online_config = { workspace = true }
openssl = "0.10"
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = { version = "2.8", features = ["bytes"] }
rand = "0.8"
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
# better to not use slog-global, but pass in the logger
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["time", "rt"] }

[dev-dependencies]
matches = "0.1.8"
tempfile = "3.1"
test_util = { workspace = true }
toml = "0.5"
