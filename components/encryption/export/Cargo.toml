[package]
name = "encryption_export"
version = "0.0.1"
edition = "2018"
publish = false

[dependencies]
async-trait = "0.1"
cloud = { workspace = true }
derive_more = "0.99.3"
encryption = { workspace = true }
error_code = { workspace = true }
file_system = { workspace = true }
kvproto = { workspace = true }
openssl = "0.10"
protobuf = { version = "2.8", features = ["bytes"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
# better to not use slog-global, but pass in the logger
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_util = { workspace = true }

[dev-dependencies]
rust-ini = "0.14.0"
structopt = "0.3"
