[package]
name = "cdc"
version = "0.0.1"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
test-engine-kv-rocksdb = [
  "tikv/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "tikv/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "tikv/test-engines-rocksdb",
]
test-engines-panic = [
  "tikv/test-engines-panic",
]
tcmalloc = ["tikv/tcmalloc"]
jemalloc = ["tikv/jemalloc"]
mimalloc = ["tikv/mimalloc"]
snmalloc = ["tikv/snmalloc"]
portable = ["tikv/portable"]
sse = ["tikv/sse"]
mem-profiling = ["tikv/mem-profiling"]
failpoints = ["tikv/failpoints"]

[dependencies]
api_version = { workspace = true }
bitflags = "1.0"
causal_ts = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crossbeam = "0.8"
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
fail = "0.5"
futures = "0.3"
futures-timer = "3.0"
getset = "0.1"
grpcio = { workspace = true }
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
online_config = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raftstore = { workspace = true }
resolved_ts = { workspace = true }
security = { workspace = true }
semver = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv = { workspace = true }
tikv_kv = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread", "time"] }
txn_types = { workspace = true }

[dev-dependencies]
criterion = "0.3"
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
tempfile = "3.0"
test_pd_client = { workspace = true }
test_raftstore = { workspace = true }
test_util = { workspace = true }

[[test]]
name = "integrations"
path = "tests/integrations/mod.rs"

# To avoid failpoints interfere with normal tests, we separate them.
[[test]]
name = "failpoints"
path = "tests/failpoints/mod.rs"
required-features = ["failpoints"]

[[bench]]
name = "cdc_event"
path = "benches/cdc_event.rs"
harness = false
