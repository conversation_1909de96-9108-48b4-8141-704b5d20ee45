[package]
name = "engine_rocks_helper"
version = "0.1.0"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]

[dependencies]
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
fail = "0.5"
futures = "0.3"
keys = { workspace = true }
lazy_static = "1.4.0"
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = "2.8"
raftstore = { workspace = true }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_util = { workspace = true }

[dev-dependencies]
engine_test = { workspace = true }
kvproto = { workspace = true }
tempfile = "3.0"
