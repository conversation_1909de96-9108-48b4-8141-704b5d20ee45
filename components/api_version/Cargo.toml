[package]
name = "api_version"
version = "0.1.0"
edition = "2018"
publish = false

[features]
testexport = []

[dependencies]
bitflags = "1.0.1"
codec = { workspace = true }
engine_traits = { workspace = true }
kvproto = { workspace = true }
match-template = "0.0.1"
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
txn_types = { workspace = true }

[dev-dependencies]
panic_hook = { workspace = true }
