[package]
name = "engine_panic"
version = "0.0.1"
description = "An example TiKV storage engine that does nothing but panic"
edition = "2018"
publish = false

[dependencies]
engine_traits = { workspace = true }
kvproto = { workspace = true }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
tikv_alloc = { workspace = true }
# FIXME: Remove this dep from the engine_traits interface
tikv_util = { workspace = true }
tracker = { workspace = true }
txn_types = { workspace = true }
